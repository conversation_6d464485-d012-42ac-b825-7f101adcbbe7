import { useState, useEffect, useCallback, useRef } from "react";
import {
  Upload,
  Button,
  Typography,
  Card,
  Spin,
  Input,
  message,
  Form,
  Flex,
  Menu,
  Select,
  Modal,
  Tabs,
} from "antd";
import {
  CheckCircleFilled,
  InboxOutlined,
  DeleteOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import {
  uploadFile,
  templatePadding,
  downloadPaddingResult,
} from "@/api/template";
import "./index.less";
import CodeMirror from "@uiw/react-codemirror";
import { markdown } from "@codemirror/lang-markdown";
import { developProcess } from "@/api/developProcessV2";
import { extractJSONFromString } from "@/utils/json-extractor.ts";
import ReactMarkdown from "react-markdown";
import RemarkMath from "remark-math";
import RemarkGfm from "remark-gfm";
import RemarkBreaks from "remark-breaks";
import { dark } from "react-syntax-highlighter/dist/esm/styles/prism";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import Mermaid from "./components/mermaid";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
// import StreamTypewriter from "@/component/StreamTypewriter";
import ThinkBlock from "@/component/think";
import { useNavigate } from "react-router-dom";

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

type Section =
  | "需求拆解"
  | "功能清单"
  | "原型设计"
  | "库表设计"
  | "接口文档"
  | "API开发"
  | "测试用例"
  | "实体关系图";
type Status = "未开始" | "进行中" | "已生成";

interface SectionState {
  title: Section;
  status: Status;
  result?: string;
  disabled: boolean;
  message: string;
  prototypeContent?: {
    [key: string]: string;
  };
}

interface TabItem {
  code: string | string[];
  title: string;
  html: string;
  css: string;
}

const token = import.meta.env["VITE_DEVELOP_PROCESS_AIDS"] || "";

export const DevelopProcessAIDS = () => {
  const navigate = useNavigate();

  const [messageApi, contextHolder] = message.useMessage();
  const [contractContent, setContractContent] = useState<string>("");
  const [uploadedFiles, setUploadedFiles] = useState<
    { id: string; name: string }[]
  >([]);

  const [projectName, setProjectName] = useState<string>("");
  const [projectContext, setProjectContext] = useState<string>("");
  const [requirementDescription, setRequirementDescription] =
    useState<string>("");
  // 在组件顶部添加一个计数器状态
  const [regenerateCount, setRegenerateCount] = useState(0);
  // 添加一个新的状态来存储当前的流式内容
  const [streamContent, setStreamContent] = useState("");
  const [modalOpen, setModalOpen] = useState(false);
  const [pictureType, setPictureType] = useState("系统框架描述");
  const [graph, setGraph] = useState("用例图");
  const [style, setStyle] = useState("");
  const [isEdit, setIsEdit] = useState(false); // 是否是修改
  const scrollRef = useRef<HTMLDivElement>(null); // 滚动的div
  const [isScroll, setIsScroll] = useState(false); // 是否是点击开始生成，或者重新生成， 如果是需要滚动，否则不用
  const [tabList, setTabList] = useState<TabItem[]>([]); // 功能页面tab
  const [currentTab, setCurrentTab] = useState("0"); // 功能页面当前活跃tab
  useEffect(() => {
    if (uploadedFiles.length > 0) {
      messageApi.open({
        key: "uploading",
        type: "success",
        content: "文件上传成功",
        duration: 1,
      });
    }
  }, [uploadedFiles, messageApi]);

  const [selectedSection, setSelectedSection] = useState<Section>("需求拆解");
  const [sections, setSections] = useState<SectionState[]>([
    { title: "需求拆解", status: "未开始", disabled: false, message: "" },
    { title: "功能清单", status: "未开始", disabled: true, message: "" },
    {
      title: "原型设计",
      status: "未开始",
      disabled: true,
      message: "",
      prototypeContent: {
        系统框架描述: "",
        系统框架代码: "",
        功能页面: "",
      },
    },
    { title: "库表设计", status: "未开始", disabled: true, message: "" },
    { title: "接口文档", status: "未开始", disabled: true, message: "" },
    { title: "API开发", status: "未开始", disabled: true, message: "" },
    { title: "测试用例", status: "未开始", disabled: true, message: "" },
    { title: "实体关系图", status: "未开始", disabled: true, message: "" },
  ]);

  const [requirementDisassemblyQuery, setRequirementDisassemblyQuery] =
    useState<string>("");
  const [functionListQuery, setFunctionListQuery] = useState<string>("");
  const [interfaceDocumentationQuery, setInterfaceDocumentationQuery] =
    useState<string>("");

  // 获取数据
  const handleGeneration = useCallback(
    // async (fileIds: string[]) => {
    async () => {
      setGenerating(true);
      let accumulatedMessages = "";
      // 清空流式内容
      setStreamContent("");
      // 清空tab列表
      setTabList([]);
      // 重置当前tab
      setCurrentTab("0");
      let inputs = {};
      let query = "";
      switch (selectedSection) {
        case "需求拆解":
          query = requirementDescription;
          break;
        case "功能清单":
          query = requirementDisassemblyQuery;
          break;
        case "原型设计":
          query = functionListQuery;
          inputs = {
            style,
            pictureType,
          };
          break;
        case "库表设计":
        case "测试用例":
          query = functionListQuery;
          break;
        case "API开发":
          query = interfaceDocumentationQuery;
          break;
        case "接口文档":
          query = functionListQuery;
          break;
        case "实体关系图":
          query = functionListQuery;
          inputs = {
            style,
            graph,
          };
          break;
        default:
          break;
      }
      // 赋值 每次都带
      const obj = {
        requirementList: "",
        featureList: "",
        dateDesign: "",
        apiDesign: "",
        develop: "",
        testDesign: "",
      };
      sections.forEach((item) => {
        if (item.title == "需求拆解") {
          obj.requirementList = item.message;
        } else if (item.title == "功能清单") {
          obj.featureList = item.message;
        } else if (item.title == "库表设计") {
          obj.dateDesign = item.message;
        } else if (item.title == "接口文档") {
          obj.apiDesign = item.message;
        } else if (item.title == "API开发") {
          // obj.develop = item.message;
        } else if (item.title == "测试用例") {
          obj.testDesign = item.message;
        }
      });
      const newSections = sections.map((s) => {
        if (s.title === selectedSection) {
          return { ...s, status: "进行中" as Status, message: "" };
        }
        return s;
      });
      setSections(newSections);
      setIsScroll(true);
      try {
        await developProcess(
          {
            type: selectedSection,
            background: projectContext,
            ...obj,
            query: query,
            dataModel: sections.filter((v) => {
              return v.title == "库表设计";
            })[0].message,
            ...inputs,
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                accumulatedMessages += text;
                const cleanedData = accumulatedMessages.replace(
                  /^```markdown\s*|```$/g,
                  ""
                );
                // setMarkdownTable(cleanedData)
                // console.log(accumulatedMessages);
                // setMessages((prev) => prev + text);
                // 更新流式内容
                // 只有当选中原型设计且类型为功能页面时才处理tab数据
                if (
                  selectedSection === "原型设计" &&
                  pictureType === "功能页面"
                ) {
                  // 实时处理流式数据，检测title并创建tab
                  setTabList((prevTabs) => {
                    const newTabs = [...prevTabs];

                    // 检测所有title标签
                    const titleMatches = [
                      ...cleanedData.matchAll(/<title>(.*?)<\/title>/gi),
                    ];

                    titleMatches.forEach((titleMatch, titleIndex) => {
                      const title = titleMatch[1];

                      // 检查是否已存在该tab
                      const existingTabIndex = newTabs.findIndex(
                        (tab) => tab.title === title
                      );

                      if (existingTabIndex === -1) {
                        // 创建新tab
                        newTabs.push({
                          title,
                          html: "",
                          css: "",
                          code: "",
                        });
                        // 设置当前活跃tab为新创建的tab
                        setCurrentTab((newTabs.length - 1).toString());
                      }
                    });

                    // 更新现有tabs的内容
                    newTabs.forEach((tab, tabIndex) => {
                      if (tab.title) {
                        // 查找该title对应的HTML内容
                        const titlePattern = new RegExp(
                          `<title>${tab.title.replace(
                            /[.*+?^${}()|[\]\\]/g,
                            "\\$&"
                          )}</title>`,
                          "i"
                        );
                        const titleMatch = cleanedData.match(titlePattern);

                        if (titleMatch) {
                          const titlePosition = cleanedData.indexOf(
                            titleMatch[0]
                          );

                          // 找到包含该title的HTML块
                          const htmlBlockPattern =
                            /```html([\s\S]*?)(?:```|$)/g;
                          let htmlMatch;
                          let currentHtml = "";

                          while (
                            (htmlMatch = htmlBlockPattern.exec(cleanedData)) !==
                            null
                          ) {
                            const htmlContent = htmlMatch[1];
                            if (htmlContent.includes(titleMatch[0])) {
                              currentHtml = htmlContent.trim();
                              break;
                            }
                          }

                          // 查找对应的CSS内容
                          const cssPattern = new RegExp(
                            `<title>${tab.title.replace(
                              /[.*+?^${}()|[\]\\]/g,
                              "\\$&"
                            )}</title>[\\s\\S]*?\`\`\`\\s*\`\`\`css([\\s\\S]*?)(?:\`\`\`|$)`,
                            "i"
                          );
                          const cssMatch = cleanedData.match(cssPattern);
                          const currentCss = cssMatch ? cssMatch[1].trim() : "";

                          // 更新tab内容
                          newTabs[tabIndex] = {
                            ...tab,
                            html: currentHtml,
                            css: currentCss,
                            code: `${currentHtml}\n\n<style>\n${currentCss}\n</style>`,
                          };
                        }
                      }
                    });

                    return newTabs;
                  });
                }

                setStreamContent(cleanedData);
                // 立即更新当前部分的内容
                setSections((prevSections) =>
                  prevSections.map((s) =>
                    s.title === selectedSection
                      ? {
                          ...s,
                          message: cleanedData,
                          ...(selectedSection === "原型设计" && {
                            prototypeContent: {
                              ...s.prototypeContent,
                              [pictureType]: cleanedData,
                            },
                          }),
                        }
                      : s
                  )
                );
                switch (selectedSection) {
                  case "需求拆解":
                    setRequirementDisassemblyQuery(accumulatedMessages);
                    break;
                  case "功能清单":
                    setFunctionListQuery(accumulatedMessages);
                    break;
                  case "接口文档":
                    setInterfaceDocumentationQuery(accumulatedMessages);
                    break;
                  default:
                    break;
                }
              }
              if (finished) {
                setGenerating(false);
                // setMessagesEnd(true)
                // 改进后的版本
                setSections((prevSections) => {
                  // 第一步：标记当前选中section为已完成
                  const afterFirstUpdate = prevSections.map((s) =>
                    s.title === selectedSection
                      ? {
                          ...s,
                          status: "已完成" as Status,
                          message: accumulatedMessages,
                          ...(selectedSection === "原型设计" && {
                            prototypeContent: {
                              ...s.prototypeContent,
                              [pictureType]: accumulatedMessages,
                            },
                          }),
                        }
                      : s
                  );

                  // 第二步：根据选中section进行额外处理
                  switch (selectedSection) {
                    case "需求拆解":
                      return afterFirstUpdate.map((s) =>
                        s.title === "功能清单" ? { ...s, disabled: false } : s
                      );

                    case "功能清单":
                      return afterFirstUpdate.map((s) =>
                        [
                          "原型设计",
                          "库表设计",
                          "接口文档",
                          "测试用例",
                          "实体关系图",
                        ].includes(s.title)
                          ? { ...s, disabled: false }
                          : s
                      );

                    case "接口文档":
                      return afterFirstUpdate.map((s) =>
                        s.title === "API开发" ? { ...s, disabled: false } : s
                      );

                    default:
                      return afterFirstUpdate;
                  }
                });
                try {
                  // const list = JSON.parse(accumulatedMessages)
                  // list.forEach((item: { key: any }, index: any) => {
                  //   item.key = index
                  // })
                  // setTableList(list)
                  // const cleanedData = accumulatedMessages.replace(
                  //   /^```markdown\s*|```$/g,
                  //   ''
                  // )
                  // setMarkdownTable(cleanedData)
                } catch (e) {
                  console.log(accumulatedMessages);
                  // setMessages(accumulatedMessages);
                }
              }
            },
            onError: () => {
              const newSections = sections.map((s) => {
                if (s.title === selectedSection) {
                  return { ...s, status: "已完成" as Status };
                }
                return s;
              });
              setSections(newSections);
              setGenerating(false);
            },
            onFinish: () => {
              setIsScroll(false);
              setGenerating(false);
            },
          }
        );
      } catch (err) {
        const newSections = sections.map((s) => {
          if (s.title === selectedSection) {
            return { ...s, status: "已完成" as Status };
          }
          return s;
        });
        setSections(newSections);
        setGenerating(false);
      }
    },
    [
      contractContent,
      selectedSection,
      projectContext,
      requirementDescription,
      requirementDisassemblyQuery,
      functionListQuery,
      interfaceDocumentationQuery,
      sections,
      style,
      pictureType,
    ]
  );

  // 依赖检查
  const checkDependencies = (section: Section): boolean => {
    const getStatus = (title: Section) =>
      sections.find((s) => s.title === title)?.status === "已完成";

    switch (section) {
      case "需求拆解":
        // 验证必备条件是否满足
        if (!projectName || !projectContext || !requirementDescription) {
          messageApi.warning("请填写项目名称、项目背景和需求描述");
          return false;
        }
        return true;
      case "功能清单":
        return getStatus("需求拆解");
      case "原型设计":
        return getStatus("需求拆解") && getStatus("功能清单");
      case "库表设计":
      case "接口文档":
      case "测试用例":
        return getStatus("功能清单");
      case "API开发":
        return getStatus("库表设计") && getStatus("接口文档");
      case "实体关系图":
        return getStatus("需求拆解") && getStatus("功能清单");
      default:
        return false;
    }
  };

  // 选择步骤
  const handleSelectSection = (section: Section) => {
    console.log("选择工具条", section);
    const checkDepen = checkDependencies(section);
    console.log("依赖检查", checkDepen);
    // 设置当前步骤
    setSelectedSection(section);

    // 如果该部分已经有内容，同步到 streamContent
    const selectedSectionData = sections.find((s) => s.title === section);
    if (selectedSectionData) {
      if (section === "原型设计" && selectedSectionData.prototypeContent) {
        setStreamContent(
          selectedSectionData.prototypeContent[pictureType] || ""
        );
      } else {
        setStreamContent(selectedSectionData.message || "");
      }
    } else {
      // 如果没有内容，清空 streamContent
      setStreamContent("");
    }
    // 检查依赖
    // if (checkDepen) {
    //   // 设置当前步骤
    //   setSelectedSection(section);
    //   // 设置状态
    //   const newSections = sections.map((s) => {
    //     if (s.title === section) {
    //       return { ...s, status: '进行中' as Status };
    //     }
    //     return s;
    //   });
    //   setSections(newSections);
    //   // 请求接口获取数据
    //   handleGeneration()
    // }else{
    //   // 需求拆解不需要提示
    //   if( section != '需求拆解' ){
    //     messageApi.warning('请先完成前置步骤');
    //   }
    // }
  };

  // 获取状态颜色
  const getStatusColor = (section: SectionState) => {
    switch (section.status) {
      case "已完成":
        return "text-green-600";
      case "进行中":
        return "text-blue-600";
      default:
        return "text-muted-foreground";
    }
  };

  //点击生成
  const handleGenerationStart = () => {
    const checkDepen = checkDependencies(selectedSection);
    if (!checkDepen) {
      return;
    }
    setStartSending(true);
    console.log("点击生成");
    setRegenerateCount((prev) => prev + 1); // 增加计数
    // setGenerationMetadata(null)
    // setProjectContext(projectContext)
    // setRequirementDescription(requirementDescription)
    // 清空流式内容

    // const selectedSectionData = sections.find(
    //   (s) => s.title === selectedSection
    // );
    // if (selectedSectionData) {
    //   if (
    //     selectedSection === "原型设计" &&
    //     selectedSectionData.prototypeContent
    //   ) {
    //     setStreamContent("");
    //   } else {
    //     setStreamContent("");
    //   }
    // }
    setStreamContent("");
    handleGeneration();
  };
  // // 点击导出
  // const handleExport = () => {
  //   if (
  //     sections.filter((v) => {
  //       return v.title == selectedSection
  //     })[0].message
  //   ) {
  //     setStartSending(true)
  //     console.log('点击导出')
  //     // setMarkdownTable('')
  //     // 导出内容到Word文档
  //     const resultContainer = document.getElementById('resultContainer')
  //     let text = ''
  //     if (resultContainer) {
  //       text = resultContainer.innerHTML
  //     } else {
  //       console.error('resultContainer is null')
  //       return
  //     }
  //     const blob = new Blob(['<html><body>' + text + '</body></html>'], {
  //       type: 'application/msword',
  //     })
  //     const url = URL.createObjectURL(blob)
  //     const a = document.createElement('a')
  //     a.href = url
  //     a.download = '需求拆解.doc'
  //     document.body.appendChild(a)
  //     a.click()
  //     document.body.removeChild(a)
  //     URL.revokeObjectURL(url)
  //   } else {
  //     messageApi.warning('请先生成' + selectedSection)
  //   }
  // }
  // 获取数据
  const handleExportGeneration = useCallback(async () => {
    setGenerating(true);

    // 检查是否有选中的section和消息
    const selectedSectionData = sections.find(
      (v) => v.title === selectedSection
    );
    if (!selectedSectionData?.message) {
      messageApi.warning("请先生成" + selectedSection);
      setGenerating(false);
      return;
    }
    console.log("selectedSection", selectedSection);
    console.log("selectedSectionData", selectedSectionData);
    // setGenerating(false)
    // return

    // 检查是否有上传的文件
    if (!uploadedFiles.length) {
      messageApi.warning("请先上传模板文件");
      setGenerating(false);
      return;
    }
    setModalOpen(false);

    let res = "";
    // 赋值 每次都带
    const obj = {
      requirementList: "",
      featureList: "",
      dateDesign: "",
      apiDesign: "",
      develop: "",
      testDesign: "",
    };
    sections.forEach((item) => {
      if (item.title == "需求拆解") {
        obj.requirementList = item.message;
      } else if (item.title == "功能清单") {
        obj.featureList = item.message;
      } else if (item.title == "库表设计") {
        obj.dateDesign = item.message;
      } else if (item.title == "接口文档") {
        obj.apiDesign = item.message;
      } else if (item.title == "API开发") {
        // obj.develop = item.message;
      } else if (item.title == "测试用例") {
        obj.testDesign = item.message;
      }
    });
    try {
      await developProcess(
        {
          type: "插入模板",
          resource: selectedSectionData.message,
          ...obj,
          model: {
            type: "document",
            transfer_method: "local_file",
            upload_file_id: uploadedFiles[0].id,
          },
          background: projectContext,
        },
        {
          onMessage: (text: string | null, finished: boolean) => {
            if (text) {
              res += text;
              console.log(res, "res");
            }
            if (finished) {
              setGenerating(false);
              // 提取()中的内容
              const parenthesesContent = res.match(/\((.*?)\)/);
              const parenthesesResult = parenthesesContent
                ? parenthesesContent[1]
                : null;

              // 提取[]中的内容
              const squareBracketsContent = res.match(/\[(.*?)\]/);
              const squareBracketsResult = squareBracketsContent
                ? squareBracketsContent[1]
                : null;

              if (parenthesesResult && squareBracketsResult) {
                const link = document.createElement("a");
                link.href = `https://copilot.sino-bridge.com${parenthesesResult}`; // 本地调试
                // link.href = parenthesesResult
                link.download = `${squareBracketsResult}`;
                document.body.appendChild(link);
                link.click();
                link.remove();
              }
            }
          },
          onError: () => {
            setGenerating(false);
          },
          onFinish: () => {
            setGenerating(false);
          },
        }
      );
    } catch (err) {
      console.error("生成模板失败:", err);
      messageApi.error("生成模板失败");
      setGenerating(false);
    }
  }, [sections, selectedSection, uploadedFiles, messageApi]);
  const handleExport = () => {
    handleExportGeneration();
  };
  // 点击【开始生成】后，状态变更
  const [startSending, setStartSending] = useState<boolean>(false);

  // 生成状态
  const [generating, setGenerating] = useState<boolean>(false);
  // 生成异常
  const [selectedType, setSelectedType] = useState<number>();
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>();
  const [thankText, setThankText] = useState<string>("");

  // 把style标签移动到html标签内部
  const moveStyleIntoHtmlBlock = (input) => {
    const htmlMatch = input.match(/<html[^>]*>([\s\S]*?)<\/html>/i);
    const styleMatch = input.match(/<style[^>]*>([\s\S]*?)<\/style>/i);

    if (!htmlMatch || !styleMatch) return input; // 如果没匹配上就原样返回

    const htmlContent = htmlMatch[1];
    const styleTag = styleMatch[0];

    const newHtml = `<html>${htmlContent}${styleTag}</html>`;
    return newHtml;
  };

  // 点击预览页面
  const handlePreview = () => {
    const prototypeSection = sections.find((s) => s.title === "原型设计");
    if (prototypeSection?.prototypeContent?.[pictureType]) {
      // 提取代码内容
      let html = "";
      // 提取纯 HTML 部分
      let htmlContent = "";
      if (pictureType == "系统框架代码") {
        const codeContent = prototypeSection.prototypeContent[pictureType];
        if (codeContent.includes("```html")) {
          html = codeContent;
        } else {
          html =
            "```html\n" +
            codeContent.split("</html>")[0] +
            "</html>\n" +
            codeContent.split("</html>")[1] +
            "\n```";
        }
        htmlContent = html.match(/```html\n([\s\S]*?)```/)?.[1] || "";
      } else if (pictureType == "功能页面") {
        htmlContent = moveStyleIntoHtmlBlock(tabList[Number(currentTab)].code);
      }
      // console.log(htmlContent, "htmlContent");
      // 将代码内容转换为 Base64 编码，以便安全传递
      const encodedCode = btoa(encodeURIComponent(htmlContent));
      // 在新窗口打开预览页面
      window.open(
        `https://copilot.sino-bridge.com/toolbox/#/interpreter?type=html&code=${encodedCode}`,
        "_blank"
      );
    } else {
      messageApi.warning(`请先生成${pictureType}内容`);
    }
  };
  // 上传文件前处理（仿contract-review）
  const beforeUpload = (file: File) => {
    const ext = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    if (!["docx", "pdf"].includes(ext)) {
      message.error(
        "目前仅支持.docx, .pdf类型的文件，请您将文件转成这些格式后再次进行上传"
      );
      return false;
    }
    message.loading({ key: "uploading", content: "文件上传中" });
    uploadFile(file, token).then(async (response) => {
      message.destroy("uploading");
      if (response.id) {
        setUploadedFiles([{ id: response.id, name: file.name }]);
        message.success("文件上传成功");
      } else {
        message.error("文件上传失败");
      }
    });
    return false;
  };

  const handleDelete = (fileId: string) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((file) => file.id !== fileId)
    );
  };
  const handleCopyClick = () => {
    if (
      sections.filter((v) => {
        return v.title == selectedSection;
      })[0].message
    ) {
      let resultContainer: any = "";
      if (
        selectedSection == "API开发" ||
        (selectedSection == "原型设计" && pictureType != "系统框架描述")
      ) {
        resultContainer = document.getElementById("resultContainer");
      } else {
        resultContainer = document
          .getElementById("resultContainer")
          ?.querySelector(".cm-content");
      }

      const text = resultContainer ? resultContainer.innerText : "";
      navigator.clipboard.writeText(text).then(
        () => {
          alert("内容已复制到剪贴板");
        },
        (err) => {
          console.error("复制失败", err);
        }
      );
    } else {
      messageApi.warning("请先生成" + selectedSection);
    }
  };
  const handleGenerateTemplate = () => {
    setModalOpen(true);
    setUploadedFiles([]);
  };

  // 获取步骤对应状态
  const getStatusBySection = (section: Section) => {
    return sections.find((s) => s.title === section)?.status;
  };

  const handleTemplatePadding = () => {
    if (selectedType === 4) {
      const link = document.createElement("a");
      link.href = `https://copilot.sino-bridge.com/toolbox/%E7%94%B5%E5%AD%90%E4%BF%9D%E5%8D%95.pdf`;
      link.download = "";
      document.body.appendChild(link);
      link.click();
      link.remove();
      return;
    }
    const templatePaddingData: string | null = extractJSONFromString(messages);
    if (selectedTemplateId && templatePaddingData) {
      templatePadding(selectedTemplateId, templatePaddingData).then(
        (response) => {
          console.log(response);
          if (response.code === 200) {
            const url: string = response.data.shortUrl;
            if (url) {
              downloadPaddingResult(url);
            }
          }
        }
      );
    }
  };
  const isMermaidComplete = (code) => {
    if (!code) return false;

    const trimmed = code.trim();

    // 只要包含常见的 Mermaid 图类型关键词（忽略大小写）
    const keywords = [
      "graph",
      "sequenceDiagram",
      "classDiagram",
      "stateDiagram",
      "gantt",
      "pie",
      "journey",
      "requirementDiagram",
      "erDiagram",
      "usecaseDiagram",
    ];
    const hasKeyword = keywords.some((k) => new RegExp(k, "i").test(trimmed));
    if (!hasKeyword) return false;

    // 至少有一条箭头或者关系线，常见连接符
    if (!/(-->|---|==>|<--|<==|<->|--|\.{1,2}>)/.test(trimmed)) return false;

    // 去除空行后，代码行数不少于3行，避免空或者太短的内容
    const lines = trimmed.split("\n").filter((line) => line.trim() !== "");
    if (lines.length < 3) return false;

    // 最后一行字符数大于等于3（避免太短的代码）
    const lastLine = lines[lines.length - 1];
    if (lastLine.trim().length < 3) return false;

    // 通过以上简单检测则认为完整
    return true;
  };

  const markdownComponents = {
    think: (props: any) => {
      return <ThinkBlock finished={false}>{props.children}</ThinkBlock>;
    },
    code({ inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || "");
      const lang = match?.[1];
      const codeStr = String(children).replace(/\n$/, "");
      // ✅ 如果是 mermaid，就用图表组件渲染
      console.log(codeStr, 4444);
      if (!inline && (lang === "MERMAID" || lang === "mermaid")) {
        console.log(1111);
        console.log(isMermaidComplete(codeStr));
        if (isMermaidComplete(codeStr)) {
          return <Mermaid chart={codeStr} />;
        } else {
          return <div>加载中</div>;
        }
      }
      return !inline && match ? (
        <SyntaxHighlighter
          {...props}
          className="editor custom-scrollbar"
          language={match?.[1]}
          showLineNumbers={true}
          wrapLines={true}
          style={dark}
          customStyle={{
            border: "none",
            margin: "0",
          }}
          children={String(children).replace(/\n$/, "")}
        ></SyntaxHighlighter>
      ) : (
        <code {...props} className={className}>
          {children}
        </code>
      );
    },
  };

  const [collapse, setCollapse] = useState(false);
  const handleCollapse = () => {
    // 处理收起逻辑
    setCollapse(!collapse);
  };
  const handleChange = (value: string) => {
    console.log(`selected ${value}`);
    const prototypeSection = sections.find((s) => s.title === "原型设计");
    // 重置当前tab
    setCurrentTab("0");
    // 确保在设置新的 pictureType 后再更新内容
    setTimeout(() => {
      if (prototypeSection?.prototypeContent) {
        const content = prototypeSection.prototypeContent[value] || "";
        console.log("Setting content:", content);
        setPictureType(value);
        // 同时更新 sections 中的 message
        setSections((prevSections) =>
          prevSections.map((s) =>
            s.title === "原型设计" ? { ...s, message: content } : s
          )
        );
        setStreamContent(content);
      }
    }, 0);
  };

  useEffect(() => {
    if (isScroll) {
      scrollRef.current?.scrollTo({
        top: scrollRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [streamContent, isScroll]);

  return (
    <>
      {contextHolder}
      {/* 添加一个包装 div，带有特定的类名 */}
      <div className="develop-process-page">
        {/* {contextHolder}
        当前步骤---{selectedSection}***{getStatusBySection(selectedSection)}%% */}
        {/* {projectContext} */}
        {/* <Spin tip="处理中..." spinning={generating} fullscreen size="large" /> */}
        <div className="develop-process-container">
          {/* 左侧面板 */}
          <div className="develop-left-panel">
            <Typography.Text className="title-text">
              开发流程辅助工具
            </Typography.Text>
            {/* <Card className="input-section"> */}
            <Form layout="vertical">
              <Form.Item label="项目名称">
                <Input
                  placeholder="请输入项目名称"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                />
              </Form.Item>
              <Form.Item label="项目背景">
                <TextArea
                  rows={4}
                  placeholder="请输入项目背景"
                  value={projectContext}
                  onChange={(e) => setProjectContext(e.target.value)}
                />
              </Form.Item>
              <Form.Item label="需求描述">
                <TextArea
                  rows={4}
                  placeholder="请输入需求描述"
                  value={requirementDescription}
                  onChange={(e) => setRequirementDescription(e.target.value)}
                />
              </Form.Item>
              {selectedSection === "原型设计" && (
                <>
                  <Form.Item label="原型图风格偏好">
                    <Input
                      placeholder="原型图风格偏好"
                      value={style}
                      onChange={(e) => setStyle(e.target.value)}
                    />
                  </Form.Item>
                  <Form.Item label="原型图任务类型">
                    <Select
                      value={pictureType}
                      onChange={handleChange}
                      options={[
                        {
                          value: "系统框架描述",
                          label: "系统框架描述",
                          disabled: false,
                        },
                        {
                          value: "系统框架代码",
                          label: "系统框架代码",
                          disabled: !sections.find(
                            (s) => s.title === "原型设计"
                          )?.prototypeContent?.["系统框架描述"],
                        },
                        {
                          value: "功能页面",
                          label: "功能页面",
                          disabled: !sections.find(
                            (s) => s.title === "原型设计"
                          )?.prototypeContent?.["系统框架代码"],
                        },
                      ]}
                    />
                  </Form.Item>
                </>
              )}
              {selectedSection === "实体关系图" && (
                <>
                  <Form.Item label="实体关系图">
                    <Select
                      value={graph}
                      onChange={(key) => {
                        setGraph(key);
                      }}
                      options={[
                        {
                          value: "用例图",
                          label: "用例图",
                        },
                        {
                          value: "ER图",
                          label: "ER图",
                        },
                        {
                          value: "前端实体关系图",
                          label: "前端实体关系图",
                        },
                        {
                          value: "后端实体关系图",
                          label: "后端实体关系图",
                        },
                      ]}
                    />
                  </Form.Item>
                </>
              )}
            </Form>

            <Menu
              mode="inline"
              selectedKeys={[selectedSection]}
              onClick={({ key }) => handleSelectSection(key as Section)}
            >
              {sections.map((section) => (
                <Menu.Item
                  key={section.title}
                  className="cursor-pointer"
                  disabled={section.disabled}
                  // onClick={() => handleSelectSection(section.title)}
                >
                  <span className={getStatusColor(section)}>
                    {section.title}
                    {/* {section.status!='未开始' && <span className='menu-sts'>{section.status}</span>} */}
                    <span className="menu-sts">{section.status}</span>
                  </span>
                </Menu.Item>
              ))}
            </Menu>

            {/* </Card> */}
          </div>

          {/* 右侧面板 */}
          <div className="develop-right-panel">
            <div className="develop-action-buttons">
              <Typography.Text className="develop-section-title">
                {selectedSection}
              </Typography.Text>
              <div>
                {!(
                  selectedSection === "API开发" ||
                  (selectedSection === "原型设计" &&
                    pictureType !== "系统框架描述")
                ) &&
                  getStatusBySection(selectedSection) != "未开始" && (
                    <Button
                      type="primary"
                      onClick={() => {
                        setIsEdit(!isEdit);
                        if (isEdit) {
                          // 如果修改改完点击预览将数据同步
                          const selectedSectionData = sections.find(
                            (s) => s.title === selectedSection
                          );
                          if (selectedSectionData) {
                            if (
                              selectedSection === "原型设计" &&
                              selectedSectionData.prototypeContent
                            ) {
                              setStreamContent(
                                selectedSectionData.prototypeContent[
                                  pictureType
                                ] || ""
                              );
                            } else {
                              setStreamContent(
                                selectedSectionData.message || ""
                              );
                            }
                          } else {
                            // 如果没有内容，清空 streamContent
                            setStreamContent("");
                          }
                        }
                      }}
                    >
                      {isEdit ? "预览" : "编辑"}
                    </Button>
                  )}
                {selectedSection === "原型设计" &&
                  (pictureType == "系统框架代码" ||
                    pictureType == "功能页面") && (
                    <Button type="primary" onClick={handlePreview}>
                      网页预览
                    </Button>
                  )}
                {true && (
                  <Button type="primary" onClick={handleGenerationStart}>
                    {getStatusBySection(selectedSection) == "未开始"
                      ? "点击生成"
                      : "重新生成"}
                  </Button>
                )}
                {/* 只有需求拆解步骤存在该按钮
                {selectedSection == "需求拆解" && (
                  <Button color="cyan" variant="solid" onClick={handleExport}>
                    点击导出
                  </Button>
                )} */}
                <Button type="primary" onClick={handleCopyClick}>
                  点击复制
                </Button>
                <Button
                  type="primary"
                  // disabled={
                  //   getStatusBySection(selectedSection) == '未开始'
                  //     ? true
                  //     : false
                  // }
                  onClick={handleGenerateTemplate}
                >
                  生成模板
                </Button>
              </div>
            </div>

            {/* 思考过程 */}
            {/* <div className="develop-section">
              <Typography.Text className="develop-section-title">
                思考过程
              </Typography.Text>
            </div> */}
            {/* 思考过程区域 */}
            {thankText && (
              <Card className="thinking-card">
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Title
                    level={4}
                    style={{ margin: 0, display: "flex", alignItems: "center" }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      style={{
                        marginRight: 8,
                        width: 20,
                        height: 20,
                        color: "#1890ff",
                      }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                      />
                    </svg>
                    查询思考过程
                  </Title>
                  <Button
                    type="link"
                    onClick={handleCollapse}
                    icon={
                      collapse ? <CaretDownOutlined /> : <CaretUpOutlined />
                    }
                  >
                    {collapse ? "展开" : "收起"}
                  </Button>
                </div>
                {!collapse && (
                  <Paragraph
                    style={{
                      marginTop: 16,
                      backgroundColor: "#f5f5f5",
                      padding: "8px",
                      borderRadius: "4px",
                      color: "rgb(55, 65, 81)",
                    }}
                  >
                    <ReactMarkdown className="markdown-body">
                      {thankText}
                    </ReactMarkdown>
                  </Paragraph>
                )}
              </Card>
            )}

            {startSending && (
              <Flex
                className="preview-panel edit-cm-code"
                vertical
                gap="middle"
                ref={scrollRef}
              >
                {/* <Flex className='preview-header' justify='space-between' gap='middle'>
                <Select
                  className='template-select'
                  size='large'
                  placeholder={
                    generationMetadata
                      ? selectedType === 2
                        ? '信息提取完毕，请划词复制表格后粘贴使用'
                        : '文件提取完毕，请选择模板'
                      : '正在提取文件信息，请不要关闭或刷新页面'
                  }
                  options={templates}
                  disabled={!generationMetadata || selectedType === 2}
                  onChange={setSelectedTemplateId}
                />
                {selectedType !== 2 ? (
                  <Button
                    icon={<DownloadOutlined />}
                    type='primary'
                    size='large'
                    disabled={!selectedTemplateId}
                    onClick={handleTemplatePadding}
                  >
                    下 载
                  </Button>
                ) : (
                  <Button type='primary' size='large' icon={<CopyOutlined />} onClick={handleTableFormat}>
                    复制到剪贴板
                  </Button>
                )}
              </Flex> */}
                {sections.filter((v) => {
                  return v.title == selectedSection;
                })[0].message ? (
                  <Card
                    className="preview-content custom-scrollbar"
                    id="resultContainer"
                  >
                    {!isEdit ? (
                      <>
                        {/* 当选中原型设计且类型为功能页面时，使用Tab展示 */}
                        {selectedSection === "原型设计" &&
                        pictureType === "功能页面" &&
                        tabList.length > 0 ? (
                          <Tabs
                            type="card"
                            onChange={(key) => setCurrentTab(key)}
                            activeKey={currentTab}
                            items={tabList.map((tab, index) => ({
                              key: index.toString(),
                              label: tab.title || `页面${index + 1}`,
                              children: (
                                <div style={{ marginBottom: 16 }}>
                                  <SyntaxHighlighter
                                    language="html"
                                    style={dark}
                                  >
                                    {tab.html}
                                  </SyntaxHighlighter>
                                  {tab.css && (
                                    <SyntaxHighlighter
                                      language="css"
                                      style={dark}
                                    >
                                      {tab.css}
                                    </SyntaxHighlighter>
                                  )}
                                </div>
                              ),
                            }))}
                          />
                        ) : (
                          <ReactMarkdown
                            className="markdown-body custom-scrollbar"
                            remarkPlugins={[
                              [RemarkMath],
                              RemarkGfm,
                              RemarkBreaks,
                            ]}
                            components={markdownComponents}
                          >
                            {streamContent}
                          </ReactMarkdown>
                        )}
                        {/* <StreamTypewriter
                          key={`${selectedSection}-${regenerateCount}`}
                          // text={sections.filter((v) => {
                          //   return v.title == selectedSection;
                          // })[0].message}
                          text={streamContent}
                          components={markdownComponents}
                          end={!generating} // 添加这一行，当 generating 为 false 时表示输出结束
                        /> */}
                      </>
                    ) : (
                      <div>
                        <CodeMirror
                          value={
                            sections.filter((v) => {
                              return v.title == selectedSection;
                            })[0].message
                          }
                          height="300px"
                          extensions={[markdown()]}
                          onChange={(val) => {
                            setSections((prevSections) =>
                              prevSections.map((section) =>
                                section.title === selectedSection
                                  ? { ...section, message: val }
                                  : section
                              )
                            );
                            if (selectedSection == "需求拆解") {
                              setRequirementDisassemblyQuery(val);
                            } else if (selectedSection == "功能清单") {
                              setFunctionListQuery(val);
                            } else if (selectedSection == "接口文档") {
                              setInterfaceDocumentationQuery(val);
                            }
                          }}
                        />
                      </div>
                    )}
                  </Card>
                ) : sections.filter((v) => {
                    return v.title == selectedSection;
                  })[0].status == "进行中" ? (
                  <Card
                    className="preview-content custom-scrollbar"
                    id="resultContainer"
                  >
                    <>
                      <LoadingOutlined
                        style={{ fontSize: 48, marginBottom: 24 }}
                      />
                      <Typography.Text>
                        正在提取文件信息，请不要关闭或刷新页面
                      </Typography.Text>
                    </>
                  </Card>
                ) : (
                  ""
                )}
              </Flex>
            )}
          </div>
        </div>
        {/* 弹窗 */}
        <Modal
          title={
            <span style={{ fontWeight: 600, fontSize: 20 }}>上传模板</span>
          }
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          footer={null}
          width={500}
          centered
          destroyOnClose
        >
          <Upload.Dragger
            showUploadList={false}
            beforeUpload={beforeUpload}
            multiple={false}
            accept=".pdf,.docx"
            style={{ margin: "32px 0" }}
          >
            <div className="ant-upload-drag-icon">
              {uploadedFiles.length > 0 ? (
                <CheckCircleFilled />
              ) : (
                <InboxOutlined />
              )}
            </div>
            <div className="ant-upload-hint">
              <span>拖拽文件到此处上传</span>
              <br />
              <span style={{ fontSize: "12px", color: "#999" }}>
                或点击选择文件
              </span>
            </div>
          </Upload.Dragger>
          {uploadedFiles.length > 0 && (
            <div className="file-list-contract" style={{ margin: "12px 0" }}>
              {uploadedFiles.map((file) => (
                <div
                  key={file.id}
                  className="file-item"
                  style={{ display: "flex", alignItems: "center", gap: 8 }}
                >
                  <span>{file.name}</span>
                  <DeleteOutlined
                    onClick={() => handleDelete(file.id)}
                    style={{ cursor: "pointer" }}
                  />
                </div>
              ))}
            </div>
          )}
          <Button
            type="primary"
            block
            size="large"
            disabled={uploadedFiles.length === 0}
            style={{ marginTop: 16 }}
            onClick={handleExport}
          >
            生成模板
          </Button>
        </Modal>
      </div>
    </>
  );
};

export default DevelopProcessAIDS;
