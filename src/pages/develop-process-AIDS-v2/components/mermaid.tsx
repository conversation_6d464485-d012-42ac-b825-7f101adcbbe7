import React, { useEffect, useRef, useState } from "react";
import mermaid from "mermaid";

mermaid.initialize({
  startOnLoad: false,
  theme: "default",
  securityLevel: "loose",
});

const Mermaid: React.FC<{ chart: string }> = ({ chart }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (typeof window === "undefined" || !containerRef.current || !chart) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    const renderMermaid = async () => {
      try {
        const id = `mermaid-${Math.random().toString(36).slice(2)}`;

        const { svg } = await mermaid.render(id, chart);

        if (containerRef.current) {
          console.log(svg, 44);
          containerRef.current.innerHTML = svg;
        }

        setIsLoading(false);
      } catch (err: any) {
        setError(err.message || "未知错误");
        setIsLoading(false);
      }
    };

    renderMermaid();
  }, [chart]);

  return (
    <div className="mermaid-wrapper">
      {error ? (
        <pre style={{ color: "red" }}>Mermaid 渲染失败：{error}</pre>
      ) : isLoading ? (
        <div>加载中...</div>
      ) : (
        <div ref={containerRef} />
      )}
    </div>
  );
};

export default Mermaid;
